"""
Search Operators Configuration

Centralized definition of logical operators and synonyms for search functionality.
This is the single source of truth for all search complexity detection.
"""

from typing import List, Dict, Set
from dataclasses import dataclass


@dataclass
class SearchOperatorConfig:
    """Configuration for search operators and complexity detection."""
    
    # Character-based operators (highest priority for detection)
    character_operators: List[str]
    
    # Keyword operators with synonyms
    keyword_operators: Dict[str, List[str]]
    
    # Special operators requiring context
    contextual_operators: List[str]
    
    # Operators that should be ignored in certain contexts
    ignored_patterns: List[str]


# Default search operator configuration
DEFAULT_SEARCH_OPERATORS = SearchOperatorConfig(
    character_operators=[
        '|',    # OR operator (pipe)
        '(',    # Group start
        ')',    # Group end
        '&',    # AND operator
        '"',    # Quoted strings
        '+',    # Required terms
        '*',    # Wildcard
        '~',    # Fuzzy search
        '/',    # Alternative OR operator
    ],
    
    keyword_operators={
        'OR': ['OR', 'or', '|', '/'],
        'AND': ['AND', 'and', '&', '+'],
        'NOT': ['NOT', 'not', '-', '!'],
    },
    
    contextual_operators=[
        '-',    # NOT operator (context-dependent)
        '!',    # Alternative NOT
    ],
    
    ignored_patterns=[
        # Patterns that look like operators but should be ignored in certain contexts
        'OR...PHEUS',  # Example from PM notes - "OR" in "ORPHEUS" should be ignored
        'AND...ERSON', # Similar pattern for "ANDERSON"
    ]
)


class SearchComplexityDetector:
    """Detects search query complexity using centralized operator definitions."""
    
    def __init__(self, config: SearchOperatorConfig = None):
        """Initialize with operator configuration."""
        self.config = config or DEFAULT_SEARCH_OPERATORS
    
    def is_simple_query(self, query: str) -> bool:
        """Determine if query is simple enough for live filtering.
        
        Args:
            query: User input query string
            
        Returns:
            True if query can be handled with simple string matching
        """
        if not query or not query.strip():
            return True
        
        # Check for character-based operators first (fastest)
        if self._has_character_operators(query):
            return False
        
        # Check for keyword operators with word boundaries
        if self._has_keyword_operators(query):
            return False
        
        # Check for contextual operators
        if self._has_contextual_operators(query):
            return False
        
        return True
    
    def _has_character_operators(self, query: str) -> bool:
        """Check for character-based operators."""
        return any(op in query for op in self.config.character_operators)
    
    def _has_keyword_operators(self, query: str) -> bool:
        """Check for keyword operators with word boundary validation."""
        import re
        
        query_upper = query.upper()
        
        for primary_keyword, synonyms in self.config.keyword_operators.items():
            for synonym in synonyms:
                # Skip character operators (handled separately)
                if len(synonym) == 1:
                    continue
                
                # Check if keyword appears as whole word
                if re.search(r'\b' + re.escape(synonym.upper()) + r'\b', query_upper):
                    # Check if it's in an ignored pattern
                    if not self._is_ignored_pattern(query, synonym):
                        return True
        
        return False
    
    def _has_contextual_operators(self, query: str) -> bool:
        """Check for contextual operators like dash operators."""
        for op in self.config.contextual_operators:
            if op == '-':
                # Dash operator logic
                if (query.startswith('-') or ' -' in query or 
                    query.endswith(' -') or '(-' in query):
                    return True
        
        return False
    
    def _is_ignored_pattern(self, query: str, operator: str) -> bool:
        """Check if operator appears in an ignored pattern context."""
        # This could be expanded to handle more sophisticated pattern matching
        # For now, just check if the operator is part of a larger word
        
        # Simple heuristic: if operator is surrounded by letters, it might be part of a word
        import re
        
        # Find all occurrences of the operator
        for match in re.finditer(re.escape(operator), query, re.IGNORECASE):
            start, end = match.span()
            
            # Check if it's surrounded by word characters
            before_char = query[start-1] if start > 0 else ' '
            after_char = query[end] if end < len(query) else ' '
            
            # If both before and after are word characters, it's likely part of a word
            if before_char.isalnum() and after_char.isalnum():
                continue  # This occurrence is ignored
            else:
                return False  # Found a valid operator occurrence
        
        return True  # All occurrences were ignored


# Global instance for easy access
search_complexity_detector = SearchComplexityDetector()


def is_simple_query(query: str) -> bool:
    """Convenience function for simple query detection.
    
    Args:
        query: User input query string
        
    Returns:
        True if query can be handled with simple string matching
    """
    return search_complexity_detector.is_simple_query(query)


def get_all_operators() -> List[str]:
    """Get all operators for debugging/testing purposes."""
    config = DEFAULT_SEARCH_OPERATORS
    all_ops = config.character_operators.copy()
    
    for synonyms in config.keyword_operators.values():
        all_ops.extend(synonyms)
    
    all_ops.extend(config.contextual_operators)
    
    return list(set(all_ops))  # Remove duplicates


def get_operator_config() -> SearchOperatorConfig:
    """Get the current operator configuration."""
    return DEFAULT_SEARCH_OPERATORS
