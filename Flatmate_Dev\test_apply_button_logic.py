#!/usr/bin/env python3
"""
Test script to verify the apply button logic for complex queries.

This script tests that "all_columns" and "all_visible" searches are properly
detected as complex queries that should trigger the apply button.
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtCore import Qt, QTimer
    from PySide6.QtWidgets import QApplication, QComboBox
    
    # Import the toolbar components
    from fm.gui._shared_components.table_view_v2.components.toolbars.table_view_toolbar_v2 import TableViewToolbar

    print("✓ Successfully imported TableViewToolbar")
    
except ImportError as e:
    print(f"✗ Import failed: {e}")
    sys.exit(1)


class MockTableView:
    """Mock table view for testing."""
    
    def __init__(self):
        self.filter_calls = []
        self.clear_calls = []
    
    def set_column_filter(self, column, pattern):
        self.filter_calls.append((column, pattern))
    
    def clear_filters(self):
        self.clear_calls.append(True)


def test_apply_button_detection():
    """Test that all_columns search is detected as complex query."""
    
    print("\n🧪 Testing Apply Button Detection Logic...")
    
    # Create Qt application
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # Create toolbar
    toolbar = TableViewToolbar()
    mock_table = MockTableView()
    
    # Connect signals
    toolbar.filter_applied.connect(mock_table.set_column_filter)
    toolbar.filters_cleared.connect(mock_table.clear_filters)
    
    print("✓ Created toolbar and mock table")
    
    # Set up columns including all_visible option
    test_columns = ["Details", "Amount", "Date"]
    toolbar.set_columns(test_columns)
    
    print(f"✓ Set columns: {test_columns}")
    
    # Test 1: Simple query on single column should NOT trigger apply button
    print("\n📋 Test 1: Simple query on single column")
    toolbar.column_dropdown.setCurrentIndex(1)  # Select "Details" (index 1, after "All Visible Columns")
    current_column = toolbar.get_current_search_column()
    print(f"Selected column: {current_column}")
    
    has_advanced = toolbar._has_advanced_operators("coffee")
    print(f"Simple query 'coffee' detected as complex: {has_advanced}")
    
    if not has_advanced:
        print("✓ Simple query on single column correctly detected as simple")
    else:
        print("✗ Simple query incorrectly detected as complex")
        return False
    
    # Test 2: all_visible search should ALWAYS trigger apply button
    print("\n📋 Test 2: all_visible search detection")
    toolbar.column_dropdown.setCurrentIndex(0)  # Select "All Visible"
    current_column = toolbar.get_current_search_column()
    print(f"Selected column: {current_column}")

    # Simulate the column change to set complexity
    toolbar._on_search_column_changed()

    # Now check if the search field recognizes it as complex
    search_field = toolbar.search_input
    is_simple = search_field._is_likely_simple_query("coffee")
    print(f"Simple query 'coffee' on all_visible detected as simple: {is_simple}")

    if not is_simple:
        print("✓ all_visible search correctly detected as complex")
    else:
        print("✗ all_visible search incorrectly detected as simple")
        return False
    
    # Test 3: Complex query should trigger apply button regardless of column
    print("\n📋 Test 3: Complex query detection")
    toolbar.column_dropdown.setCurrentIndex(1)  # Back to single column
    current_column = toolbar.get_current_search_column()
    print(f"Selected column: {current_column}")

    # Simulate the column change to reset complexity
    toolbar._on_search_column_changed()

    # Test complex query detection
    search_field = toolbar.search_input
    is_simple = search_field._is_likely_simple_query("coffee OR tea")
    print(f"Complex query 'coffee OR tea' detected as simple: {is_simple}")

    if not is_simple:
        print("✓ Complex query correctly detected as complex")
    else:
        print("✗ Complex query incorrectly detected as simple")
        return False
    
    # Test 4: Column change to all_visible should trigger complexity detection
    print("\n📋 Test 4: Column change complexity detection")

    # Change to all_visible - this should be detected as complex
    toolbar.column_dropdown.setCurrentIndex(0)  # Select "All Visible"
    current_column = toolbar.get_current_search_column()

    # Simulate the column change
    toolbar._on_search_column_changed()

    # Test with simple text - should be complex due to column selection
    search_field = toolbar.search_input
    is_simple = search_field._is_likely_simple_query("simple text")
    print(f"Simple text on all_visible after column change detected as simple: {is_simple}")

    if not is_simple:
        print("✓ Column change to all_visible correctly triggers complexity")
    else:
        print("✗ Column change to all_visible doesn't trigger complexity")
        return False

    # Test 5: Test specific complex queries mentioned by PM
    print("\n📋 Test 5: Specific complex query tests")

    # Reset to single column for this test
    toolbar.column_dropdown.setCurrentIndex(1)  # Select single column
    toolbar._on_search_column_changed()

    # Test opening parenthesis
    is_simple_paren = search_field._is_likely_simple_query("(")
    print(f"Query '(' detected as simple: {is_simple_paren}")

    if not is_simple_paren:
        print("✓ Opening parenthesis correctly detected as complex")
    else:
        print("✗ Opening parenthesis incorrectly detected as simple")
        return False

    # Test OR...PHEUS case (should be simple since OR is part of word)
    is_simple_orpheus = search_field._is_likely_simple_query("ORPHEUS")
    print(f"Query 'ORPHEUS' detected as simple: {is_simple_orpheus}")

    if is_simple_orpheus:
        print("✓ 'ORPHEUS' correctly detected as simple (OR ignored in word)")
    else:
        print("✗ 'ORPHEUS' incorrectly detected as complex")
        return False
    
    return True


def test_column_dropdown_setup():
    """Test that the column dropdown is set up correctly."""
    
    print("\n🔍 Testing Column Dropdown Setup...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    toolbar = TableViewToolbar()
    
    # Test with multiple columns
    test_columns = ["Details", "Amount", "Date", "Category"]
    toolbar.set_columns(test_columns)
    
    # Check dropdown contents
    dropdown_items = []
    for i in range(toolbar.column_dropdown.count()):
        text = toolbar.column_dropdown.itemText(i)
        data = toolbar.column_dropdown.itemData(i)
        dropdown_items.append((text, data))
    
    print(f"Dropdown items: {dropdown_items}")
    
    # Should have "All Visible" as first item
    if dropdown_items[0][1] == "all_visible":
        print("✓ 'All Visible' correctly added as first option")
    else:
        print("✗ 'All Visible' not found or not first")
        return False
    
    # Should have individual columns
    expected_columns = set(test_columns)
    actual_columns = {data for text, data in dropdown_items[1:] if data in expected_columns}
    
    if expected_columns == actual_columns:
        print("✓ All individual columns correctly added")
    else:
        print(f"✗ Column mismatch. Expected: {expected_columns}, Got: {actual_columns}")
        return False
    
    return True


def main():
    """Run all tests."""
    
    print("🚀 Starting Apply Button Logic Tests")
    print("=" * 50)
    
    success = True
    
    # Test apply button detection logic
    if not test_apply_button_detection():
        success = False
    
    # Test column dropdown setup
    if not test_column_dropdown_setup():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All apply button tests passed!")
        print("\nKey features verified:")
        print("• ✓ all_visible search triggers apply button")
        print("• ✓ Simple queries on single columns use live filtering")
        print("• ✓ Complex queries trigger apply button regardless of column")
        print("• ✓ Opening parenthesis triggers apply button")
        print("• ✓ OR in 'ORPHEUS' correctly ignored")
        print("• ✓ Column dropdown properly configured")
        return 0
    else:
        print("❌ Some apply button tests failed.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
