# Feature Request: Auto Import Folder

## Summary

Implement an "auto import folder" feature that allows users to specify a directory for automatic detection and import of files (e.g., bank statements, CSVs) as they appear.

## Motivation

- Streamlines workflow: eliminates manual file selection/import.
- Reduces user error and friction for routine imports.
- Supports power users and automation scenarios.

## Proposed Functionality

- **User-configurable folder path** (set in app settings)
- **Automatic monitoring**: App watches the folder for new files (using OS events or polling)
- **Automatic import**: When a new file appears, validate and process it
- **Post-import actions**: Optionally move, archive, or delete files after import
- **Error handling**: Log and notify on failed imports
- **Security**: Restrict processing to trusted folders
- **UI feedback**: Clearly show import status and errors

## Open Questions

- Should the feature be opt-in or on by default?
  > It could auto create an import folder in downloads
  > certainly we could build in that functionality at least and make it configurable
  > but I had originally thought it would be an option in
  > set source in update_data
  >
- What file types/extensions should be monitored?
  pdfs, ofx and csv, at the moment we can only handle csv
  ofx_parser and pdf_parser need their own feature request docs
- How to handle import failures or malformed files?
- >> move to unrecoginsed or create a failed_imports folder and move it there
  >>
  >
- Should there be a manual override to re-import files?
  >> give me the use case thi is an open question lets burn that bridge when we get to it, one could always manually add the folder and hit process (perhaps should say import or update database)
  >>
  >
- How should conflicts (duplicate files) be handled?
  >> they are already handled
  >>
  >

## Next Steps

- Discuss technical approach (polling vs. OS events)

  >> discuss with architect
  >>
  >
- Define configuration UI

  >> for now just a pop up window
  >> but update data should show import statistics
  >> and current to dates for each unique account number
  >>
  >
- Plan for integration with current import pipeline

  >> the auto_import_manager will just send a job to dw_director
  >>
----
# >>PM comments :

## TODO
#>>  we need a document workspace folder
and a workflow
  like a new feature work bench
it could just be a folder in feature requests
or it could be a _NEW_FEATURES folder
with a sub folder 
or we clould just move the discussion document to _FEATURES in flatmate DOCS
we need a development folder with
documents on 
- current relevant code base
- architecture - dir structure and integration points
- implementation outline
- teck stack 
- research
- insights - notes on ai workflow protocol establishment or improvement and development workflow insights 




 ---

 *Created: 2025-07-20*
