 # **the main application is in the flatmate folder**
 -the terminal is git bash by default
 - the venv is in flatmate/.venv_fm313/
 - activate the venv with `source flatmate/.venv_fm313/bin/activate` if it hasnt auto activated, which it should have
 - run the app by calling the package with `fm`

## documentaion is in flatmate/DOCS/
- _ARCHITECTURE should contain guides
- _FEATURES should contain working folders
- _REPORTS should contain analysis and reports 
- (etc)
  
**suggest rules around running tests and where they should stored** -tbc 