# AppPaths Usage Guide
**Current as of:** 2025-07-20 06:03 NZST

## Quick Reference

### Accessing Application Paths
All application paths are accessed through the centralized `AppPaths` class:

```python
from fm.core.config.paths import AppPaths

# User preferences file
preferences_path = AppPaths.USER_PREFERENCES_PATH
# Returns: Path('~/.flatmate/config/preferences.yaml')

# Configuration directory
config_dir = AppPaths.CONFIG_DIR
# Returns: Path('~/.flatmate/config/')

# Data directory
data_dir = AppPaths.DATA_DIR
# Returns: Path('~/.flatmate/data/')
```

## Current Path Structure

### Core Paths
- **CONFIG_DIR** - `~/.flatmate/config/`
- **DATA_DIR** - `~/.flatmate/data/`
- **USER_PREFERENCES_PATH** - `~/.flatmate/config/preferences.yaml`

### Usage Examples

#### 1. Reading User Preferences
```python
from fm.core.config.paths import AppPaths
from fm.core.config.config import ConfigManager

# Get preferences path
prefs_path = AppPaths.USER_PREFERENCES_PATH
config = ConfigManager()
```

#### 2. Accessing Configuration
```python
# Direct access to config directory
config_path = AppPaths.CONFIG_DIR
```

#### 3. Data Storage
```python
# Data directory for application files
data_path = AppPaths.DATA_DIR
```

## Architecture Notes

### Design Principles
- **Single Source of Truth:** All paths defined in `AppPaths`
- **Centralized Management:** Changes made in one location
- **Type Safety:** All paths return `pathlib.Path` objects

### Future Improvements (Suggestions)

1. **Path Validation**
   ```python
   # Add validation methods
   AppPaths.validate_paths()
   ```

2. **Environment Overrides**
   ```python
   # Allow environment variable overrides
   AppPaths.from_env()
   ```

3. **Dynamic Path Resolution**
   ```python
   # Support for different environments
   AppPaths.for_environment('dev')
   ```

## Best Practices
- Always use `AppPaths` for path access
- Never hardcode paths directly
- Use pathlib.Path methods for path operations
- Test path access in different environments
## Related Files
- `flatmate/src/fm/core/config/paths.py` - Path definitions
- `flatmate/src/fm/core/config/config.py` - Configuration management