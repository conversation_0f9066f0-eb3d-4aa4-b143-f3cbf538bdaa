"""
Table View Toolbar - Default Implementation

This is the DEFAULT toolbar that ships with table views. It provides the standard
functionality that most table views need:

- New layout: [Column Button] [Search Input] "in:" [Column Dropdown] [Export Button]
- Internal DB columns automatically filtered out
- Smart search with performance optimization
- Easy layout tweaking and maintenance
- Consistent styling and behavior

This toolbar is designed to be the "home" toolbar - the one that works out of the box
for standard table view needs.

Usage:
    from .toolbars import TableViewToolbar
    
    toolbar = TableViewToolbar()
    toolbar.set_columns(columns, column_names)
    toolbar.filter_applied.connect(your_handler)
"""

from typing import Dict, List, Optional, Any
from PySide6.QtCore import Signal, QTimer, QSize
from PySide6.QtWidgets import QFrame, QPushButton, QLineEdit, QComboBox, QLabel
from PySide6.QtGui import QIcon

# Import the layout manager and base components
from fm.gui._shared_components.tool_bar_base import <PERSON><PERSON>barLayoutManager, BaseToolbarButton


class TableViewToolbar(QFrame):
    """Default table view toolbar implementation.
    
    This is the standard toolbar that ships with table views. It provides:
    - Modern layout with proper component arrangement
    - Smart search with debouncing for performance
    - Automatic filtering of internal database columns
    - Easy customization and maintenance
    """
    
    # Standard signals for table view toolbars
    filter_applied = Signal(object, str)  # column, pattern
    filters_cleared = Signal()
    column_visibility_requested = Signal()
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the default table view toolbar."""
        super().__init__(parent)
        self.setObjectName("TableViewToolbar")
        
        # Search debouncing timer
        self._search_timer = QTimer()
        self._search_timer.setSingleShot(True)
        self._search_timer.timeout.connect(self._emit_search_filter)
        
        # Initialize UI and connections
        self._init_ui()
        self._connect_signals()
        self._apply_styling()
    
    def _init_ui(self):
        """Initialize the UI using the layout manager for clear positioning."""
        # Initialize layout manager with table view config
        from fm.gui._shared_components.tool_bar_base import ToolbarLayoutManager
        self.layout_manager = ToolbarLayoutManager('table_view')

        # Setup the base horizontal layout
        self.layout_manager.setup_horizontal_layout(self)

        # Create widgets
        self._create_widgets()

        # Position widgets using layout manager
        self._position_widgets()

    def _create_widgets(self):
        """Create all toolbar widgets."""
        # Column visibility button
        self.column_button = QPushButton()
        self.column_button.setFixedSize(32, 32)
        self.column_button.setToolTip("Show/Hide Columns")
        self._load_column_icon()

        # Search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search...")
        self.search_input.setObjectName("ToolbarSearchInput")

        # "in:" label
        self.in_label = QLabel("in:")
        self.in_label.setObjectName("ToolbarInLabel")

        # Column dropdown
        self.column_dropdown = QComboBox()
        self.column_dropdown.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.column_dropdown.setObjectName("ToolbarColumnDropdown")

        # Export button
        self.export_button = QPushButton()
        self.export_button.setFixedSize(32, 32)
        self.export_button.setToolTip("Export Data")
        self._load_export_icon()

    def _position_widgets(self):
        """Position widgets using layout manager - CLEAR SEPARATION OF LAYOUT LOGIC."""
        # Left group: Column visibility button
        self.layout_manager.add_left_group([self.column_button], "column_controls")

        # Center group: Search components (expandable)
        self.layout_manager.add_center_group([
            self.search_input,
            self.in_label,
            self.column_dropdown
        ], "search_controls", stretch=1)

        # Right group: Export button
        self.layout_manager.add_right_group([self.export_button], "export_controls")
    
    def _load_column_icon(self):
        """Load the column visibility icon."""
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            
            # Use the nav icon for view_data (eye icon)
            icon_path = icon_manager.get_nav_icon("view_data")
            icon = IconRenderer.load_icon(icon_path, QSize(16, 16))
            self.column_button.setIcon(icon)
            self.column_button.setIconSize(QSize(16, 16))
        except Exception as e:
            print(f"Warning: Could not load column icon: {e}")
            self.column_button.setText("👁")  # Fallback emoji
    
    def _load_export_icon(self):
        """Load the export icon."""
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            
            # Try to load export icon from toolbar category
            icon_path = icon_manager.get_toolbar_icon("export")
            icon = IconRenderer.load_icon(icon_path, QSize(16, 16))
            self.export_button.setIcon(icon)
            self.export_button.setIconSize(QSize(16, 16))
        except Exception as e:
            print(f"Warning: Could not load export icon: {e}")
            self.export_button.setText("📤")  # Fallback emoji
    
    def _connect_signals(self):
        """Connect component signals."""
        # Button signals
        self.column_button.clicked.connect(self.column_visibility_requested.emit)
        self.export_button.clicked.connect(self.csv_export_requested.emit)
        
        # Search signals with smart debouncing
        self.search_input.textChanged.connect(self._on_search_text_changed)
        self.column_dropdown.currentTextChanged.connect(self._on_search_column_changed)
    
    def _on_search_text_changed(self, text: str):
        """Handle search text changes with smart debouncing."""
        # Check if we're searching all columns - this is always a complex query
        current_column = self.get_current_search_column()
        is_all_columns_search = (current_column in ["all_columns", "all_visible"])

        # For simple queries on single columns, do immediate live filtering
        # For complex queries or all_columns search, use debounced detection to avoid UI freezes
        if not is_all_columns_search and self._is_likely_simple_query(text):
            # Fast path for obviously simple queries on single columns
            self._emit_search_filter()
        else:
            # Complex query or all_columns search - use debounced detection
            self._search_timer.stop()
            if text.strip():
                self._search_timer.start(150)  # 150ms debounce for complex queries
            else:
                # Clear immediately
                self._emit_search_filter()
    
    def _on_search_column_changed(self):
        """Handle search column changes."""
        # Check if switching to/from all_columns affects complexity
        current_text = self.search_input.text().strip()
        if current_text:
            # Check if this column change affects whether we need apply button
            current_column = self.get_current_search_column()
            is_all_columns = (current_column in ["all_columns", "all_visible"])

            if is_all_columns:
                # Switching to all_columns - this becomes a complex query
                # Trigger the advanced operator detection to show apply button
                self._search_timer.stop()
                self._search_timer.start(150)  # Use debounced detection
            else:
                # Switching to single column - re-evaluate complexity
                self._emit_search_filter()
    
    def _emit_search_filter(self):
        """Emit the filter signal with current search parameters."""
        column = self.get_current_search_column()
        pattern = self.get_search_text()
        
        if column is not None:
            self.filter_applied.emit(column, pattern)
        
        # If pattern is empty, emit clear signal
        if not pattern.strip():
            self.filters_cleared.emit()
    
    def _is_likely_simple_query(self, text: str) -> bool:
        """Quick check for obviously simple queries to enable fast path."""
        if not text or not text.strip():
            return True

        # If it contains any of these characters, it's definitely not simple
        complex_chars = ['|', '/', '(', ')', '"']
        return not any(char in text for char in complex_chars)
    
    def _has_advanced_operators(self, text: str) -> bool:
        """Check if text contains advanced operators that should disable live filtering."""
        if not text or not text.strip():
            return False

        # Check if we're searching all columns - this is always considered complex
        current_column = self.get_current_search_column()
        if current_column in ["all_columns", "all_visible"]:
            return True

        # Import the search parser to use its logic
        try:
            from ..table_utils.search_query_parser import get_search_parser
            parser = get_search_parser()
            # If it's NOT a simple query, then it has advanced operators
            return not parser._is_simple_query(text)
        except ImportError:
            # Fallback to basic detection if parser not available
            return self._fallback_advanced_detection(text)
    
    def _fallback_advanced_detection(self, text: str) -> bool:
        """Fallback advanced operator detection if parser not available."""
        # Basic detection for common advanced operators
        advanced_patterns = [
            '|', '/', 'OR', 'AND', 'NOT', '(', ')', '"',
            ' - ', ' + ', ' & '
        ]
        text_upper = text.upper()
        return any(pattern in text_upper for pattern in advanced_patterns)
    
    def _apply_styling(self):
        """Apply QSS styling."""
        self.setStyleSheet("""
            QFrame#TableViewToolbar {
                background-color: #1E1E1E;
                border: 1px solid #3B8A45;
                border-radius: 4px;
                padding: 4px;
            }
            
            /* Button styling */
            QPushButton {
                background-color: transparent;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 4px;
            }
            
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-color: #3B8A45;
            }
            
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.2);
            }
            
            /* Search input styling */
            QLineEdit#ToolbarSearchInput {
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 4px 8px;
                background-color: #1E1E1E;
                color: #FFFFFF;
                font-size: 14px;
            }
            
            QLineEdit#ToolbarSearchInput:focus {
                border-color: #3B8A45;
            }
            
            /* Column dropdown styling */
            QComboBox#ToolbarColumnDropdown {
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 4px 8px;
                background-color: #1E1E1E;
                color: #FFFFFF;
                min-width: 80px;
                max-width: 200px;
            }
            
            QComboBox#ToolbarColumnDropdown:hover {
                border-color: #3B8A45;
            }
            
            /* "in:" label styling */
            QLabel#ToolbarInLabel {
                color: #CCCCCC;
                font-size: 12px;
                font-weight: normal;
            }
        """)
    
    # Public API methods
    
    def set_columns(self, columns: List[str], column_names: Optional[Dict[str, str]] = None):
        """Set available columns for the toolbar."""
        print(f"DEBUG V2 Toolbar: Received columns: {columns}")

        # Filter out internal columns
        filtered_columns = self._filter_user_columns(columns)
        print(f"DEBUG V2 Toolbar: Filtered columns: {filtered_columns}")

        # Update column dropdown
        self.column_dropdown.clear()

        # Add "All Visible Columns" option if there are multiple columns
        if len(filtered_columns) > 1:
            self.column_dropdown.addItem("All Visible Columns", "all_visible")
        
        # Add individual columns
        for col in filtered_columns:
            display_name = column_names.get(col, col) if column_names else col
            # Make display names more user-friendly
            display_name = self._format_column_display_name(display_name)
            self.column_dropdown.addItem(display_name, col)
        
        # Set default to "Details" if available, otherwise first column
        details_index = self.column_dropdown.findData("details")
        if details_index >= 0:
            self.column_dropdown.setCurrentIndex(details_index)
        elif self.column_dropdown.count() > 0:
            self.column_dropdown.setCurrentIndex(0)
    
    def _filter_user_columns(self, columns: List[str]) -> List[str]:
        """Filter out system columns that shouldn't be visible to users.

        Based on our established filtering logic, these system columns should be hidden:
        - DB UID (internal database identifier)
        - Source UID (bank-provided identifier, technical)
        - Hash (internal duplicate detection)
        - Is Deleted (system flag)
        - Import Date (system metadata)
        - Modified Date (system metadata)
        - Created At, Updated At (system timestamps)
        - Version, Internal ID, System ID (system fields)

        Args:
            columns: List of column names (display names or db names)

        Returns:
            Filtered list with system columns removed
        """
        # Comprehensive list of system columns to hide (case-insensitive)
        system_columns_to_hide = {
            'db_uid', 'source_uid', 'hash', 'is_deleted',
            'import_date', 'modified_date', 'created_at', 'updated_at',
            'version', 'internal_id', 'system_id', 'db UID', 'source UID',
            'is deleted', 'import date', 'modified date'
        }

        filtered = []
        for col in columns:
            col_lower = str(col).lower()

            # Check if this is a system column that should be hidden
            should_hide = False
            for sys_col in system_columns_to_hide:
                if col_lower == sys_col.lower():
                    should_hide = True
                    break

            # Also filter out columns starting with underscore or sys_
            if col.startswith('_') or col.startswith('sys_'):
                should_hide = True

            if not should_hide:
                filtered.append(col)

        return filtered
    
    def _format_column_display_name(self, name: str) -> str:
        """Format column names for better display."""
        if '_' in name:
            words = name.split('_')
            return ' '.join(word.capitalize() for word in words)
        return name.capitalize()
    
    def get_current_search_column(self) -> Optional[str]:
        """Get currently selected search column."""
        return self.column_dropdown.currentData()
    
    def get_search_text(self) -> str:
        """Get current search text."""
        return self.search_input.text()
    
    def clear_search(self):
        """Clear the search input."""
        self.search_input.clear()
        self.filters_cleared.emit()
    
    def get_filter_state(self) -> Dict[str, Any]:
        """Get current filter state for persistence."""
        return {
            'search_text': self.get_search_text(),
            'search_column': self.get_current_search_column(),
            'live_filtering': True
        }
    
    def set_filter_state(self, state: Dict[str, Any]):
        """Restore filter state from persistence."""
        if 'search_text' in state:
            self.search_input.setText(state['search_text'])

        if 'search_column' in state:
            column = state['search_column']
            index = self.column_dropdown.findData(column)
            if index >= 0:
                self.column_dropdown.setCurrentIndex(index)

    # Compatibility properties for legacy table view integration
    @property
    def column_group(self):
        """Compatibility property to access column button for legacy code."""
        # Create a simple object that mimics the old column_group interface
        class ColumnGroupCompat:
            def __init__(self, button):
                self.column_visibility_button = button

        return ColumnGroupCompat(self.column_button)

    def update_search_columns(self, visible_columns: List[str], column_names: Optional[Dict[str, str]] = None):
        """Update search dropdown to only show visible columns.

        This method should be called by the table view whenever column visibility changes
        to keep the search dropdown in sync with what's actually visible.

        Args:
            visible_columns: List of currently visible column identifiers
            column_names: Optional mapping of column IDs to display names
        """
        self.set_columns(visible_columns, column_names)
