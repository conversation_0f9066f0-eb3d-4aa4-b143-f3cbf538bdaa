---
Sunday, July 20, 2025 @ 04:38:43 PM
---

hitlist 

1 search logic in cat

2 clock logic in table view info bar

3 modules open at last used 

---
# 1 
---

# we need to rationalise this workspace
- I have a list of things to address:

## the search column selector should default to details
- `all visible columns` should be called simply `all visible` 
- `all searchable` is another matter requiring some thought but this was discussed in the recent debug report 
### current behaviour:
Apply button does not appear, either with `all cols` or `(` system gets laggy suggesting issue with dispatch logic and the filtering logic which should ignore logical operators 

**logical operators should be defined in one place** they should include synonymns
as defined in the documentaion, and any Operator charactor should be ignored by filtering until either - the logical operator has been ruledout: as in "OR...PHEUS " (complex logic) 
or more simply - the apply button should just be triggered and live filtering suspended.

Note: this was working in an earlier implementation ! check archives in table view folder 
Arc. question - where should this be defined !?
currently all table view utility files are together in one folder. 

## show/hide (eye icon)
- drop down should organise by a sensible 12 lines per column
- we need to refine and define it's list and where that should be defined
  - standards/Collumns ?
 - in categories and set by caller !?
*dispatched to augment*
---
...refactoring....
---
test notes: `-` alone doesnt need to signify a complex query - past tests have shown the basic search parser handles tea -coffee - biscuits fine 
the character should be a live filter ignore group though when first character of new search term 
live_filter_ignore=true check the local codebase for naming convention...
(would a pydantic class simplify things here?)

- I'm questioning whether after a term has been evaluated it can be applied automatically ... *this is speculative and should be relagted to a discussion document)

We should apply the more obvious code fixes,  tidy up the features table view workspace and record our insights
and update our discussion document and hit list ...

Notes: letter based logical operations should be in capitals to avoid confusion with terms
or should not function like OR or is a search term OR is an operator and needs evaluation
(as in ORPHEUS)
----
Sunday, July 20, 2025 @ 08:12:44 PM
---





