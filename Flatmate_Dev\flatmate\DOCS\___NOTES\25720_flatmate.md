Sunday, July 20, 2025 @ 04:38:43 PM

hitlist 

1 search logic in cat

2 clock logic in table view info bar

3 modules open at last used 

---
# 1 
---

# we need to rationalise this workspace
- I have a list of things to address:

## the search column selector should default to details
- `all visible columns` should be called simply `all visible` 
- `all searchable` is another matter requiring some thought but this was discussed in the recent debug report 
### current behaviour:
Apply button does not appear, either with `all cols` or `(` system gets laggy suggesting issue with dispatch logic and the filtering logic which should ignore logical operators 

**logical operators should be defined in one place** they should include synonymns
as defined in the documentaion, and any Operator charactor should be ignored by filtering until either - the logical operator has been ruledout: as in "OR...PHEUS " (complex logic) 
or more simply - the apply button should just be triggered and live filtering suspended.

Note: this was working in an earlier implementation ! check archives in table view folder 
Arc. question - where should this be defined !?
currently all table view utility files are together in one folder. 

## show/hide (eye icon)
- drop down should organise by a sensible 12 lines per column
- we need to refine and define it's list and where that should be defined
  - standards/Collumns ?
 - in categories and set by caller !?
*dispatched to augment*
---



